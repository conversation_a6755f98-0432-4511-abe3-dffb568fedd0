package com.example.controller;

import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.deepseek.DeepSeekChatModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
public class ChatController {
    private final DeepSeekChatModel chatModel;

    ChatMemory chatMemory;

    @Autowired
    public ChatController(DeepSeekChatModel chatModel, ChatMemory chatMemory) {
        this.chatModel = chatModel;
        this.chatMemory = chatMemory;
    }

    @GetMapping("/ai/generate")
    public Map generate(@RequestParam(value = "message", defaultValue = "Tell me a joke") String message) {
        return Map.of("generation", chatModel.call(message));
    }

    @GetMapping(value = "/ai/generateStream", produces = "text/event-stream")
    public Flux<ChatResponse> generateStream(@RequestParam(value = "message", defaultValue = "Tell me a joke") String message) {

        // 使用固定的对话ID，所有对话都在同一个会话中
        String conversationId = "main";

        // 1. 添加用户消息到记忆
        UserMessage userMessage = new UserMessage(message);
        chatMemory.add(conversationId, userMessage);

        // 2. 获取对话历史并创建prompt
        var prompt = new Prompt(chatMemory.get(conversationId));

        // 3. 流式调用并保存AI回复
        return chatModel.stream(prompt)
                .doOnComplete(() -> {
                    // 当流完成时，获取完整回复并保存到记忆
                    ChatResponse lastResponse = chatModel.call(prompt);
                    chatMemory.add(conversationId, lastResponse.getResult().getOutput());
                });
    }
}
