package com.example.config;

import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.memory.MessageWindowChatMemory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * ChatMemory配置类 - 让Spring AI自动配置JDBC存储
 *
 * <AUTHOR>
 */
@Configuration
public class ChatMemoryConfig {

    /**
     * 只需要配置ChatMemory的行为（如消息窗口大小）
     * Spring AI会自动配置JdbcChatMemoryRepository
     */
    @Bean
    public ChatMemory chatMemory() {
        return MessageWindowChatMemory.builder()
            .maxMessages(20)
            .build();
    }
}
