package com.example.config;

import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.memory.ChatMemoryRepository;
import org.springframework.ai.chat.memory.MessageWindowChatMemory;
import org.springframework.ai.chat.memory.repository.jdbc.JdbcChatMemoryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

/**
 * ChatMemory配置类 - 使用JDBC存储
 * 
 * <AUTHOR>
 */
@Configuration
public class ChatMemoryConfig {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 配置JDBC ChatMemory存储库
     */
    @Bean
    public ChatMemoryRepository chatMemoryRepository() {
        return JdbcChatMemoryRepository.builder()
            .jdbcTemplate(jdbcTemplate)
            .build();
    }

    /**
     * 配置ChatMemory，使用JDBC存储
     */
    @Bean
    public ChatMemory chatMemory(ChatMemoryRepository chatMemoryRepository) {
        return MessageWindowChatMemory.builder()
            .chatMemoryRepository(chatMemoryRepository)
            .maxMessages(20)
            .build();
    }
}
