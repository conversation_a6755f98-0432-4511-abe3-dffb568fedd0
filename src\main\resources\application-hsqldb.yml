spring:
  ai:
    deepseek:
      base-url: https://api.deepseek.com/v1
      api-key: ***********************************
      chat:
        options:
          model: deepseek-chat
          temperature: 0.8
    # ChatMemory JDBC 配置
    chat:
      memory:
        repository:
          jdbc:
            initialize-schema: always  # 让Spring AI自动创建表
  # HSQLDB 数据库配置（备用）
  datasource:
    url: **********************************  # 文件模式
    driver-class-name: org.hsqldb.jdbc.JDBCDriver
    username: sa
    password:
server:
  port: 8088
