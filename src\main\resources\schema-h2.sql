-- Spring AI Chat Memory Schema for H2 Database
CREATE TABLE IF NOT EXISTS SPRING_AI_CHAT_MEMORY (
    id VARCHAR(255) NOT NULL,
    conversation_id VARCHAR(255) NOT NULL,
    content CLOB,
    type VARCHAR(50) NOT NULL,
    metadata CLOB,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

CREATE INDEX IF NOT EXISTS idx_conversation_id ON SPRING_AI_CHAT_MEMORY(conversation_id);
CREATE INDEX IF NOT EXISTS idx_timestamp ON SPRING_AI_CHAT_MEMORY(timestamp);
