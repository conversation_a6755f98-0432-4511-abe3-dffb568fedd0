-- Spring AI Chat Memory Schema for H2 Database
CREATE TABLE IF NOT EXISTS SPRING_AI_CHAT_MEMORY (
    id VARCHAR(255) NOT NULL,
    conversation_id VARCHAR(255) NOT NULL,
    message_type VARCHAR(50) NOT NULL,
    content CLOB,
    metadata CLOB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

CREATE INDEX IF NOT EXISTS idx_conversation_id ON SPRING_AI_CHAT_MEMORY(conversation_id);
CREATE INDEX IF NOT EXISTS idx_created_at ON SPRING_AI_CHAT_MEMORY(created_at);
