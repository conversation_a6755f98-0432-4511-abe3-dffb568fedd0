hitokoto:
  jsonPath: data/hitokoto.json
image:
  jsonPath: data/image.json
  imagePath: image

spring:
  ai:
    deepseek:
      base-url: https://api.deepseek.com/v1
      api-key: ***********************************
      chat:
        options:
          model: deepseek-chat
          temperature: 0.8
    # ChatMemory JDBC 配置
    chat:
      memory:
        repository:
          jdbc:
            initialize-schema: always  # 让Spring AI自动创建表
  # H2 数据库配置
  datasource:
    url: jdbc:h2:mem:chatmemory  # 内存模式，用于测试连接
    driver-class-name: org.h2.Driver
    username: sa
    password:

  h2:
    console:
      enabled: true  # 启用H2控制台，可以通过 http://localhost:8088/h2-console 访问
server:
  port: 8088