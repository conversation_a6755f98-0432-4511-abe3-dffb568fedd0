hitokoto:
  jsonPath: data/hitokoto.json
image:
  jsonPath: data/image.json
  imagePath: image

spring:
  ai:
    deepseek:
      base-url: https://api.deepseek.com/v1
      api-key: ***********************************
      chat:
        options:
          model: deepseek-chat
          temperature: 0.8
    # ChatMemory JDBC 配置
    chat:
      memory:
        repository:
          jdbc:
            initialize-schema: always  # 让Spring AI自动创建表
  # HSQLDB 数据库配置
  datasource:
    url: **************************  # 内存模式
    driver-class-name: org.hsqldb.jdbc.JDBCDriver
    username: sa
    password:
server:
  port: 8088