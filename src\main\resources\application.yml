hitokoto:
  jsonPath: data/hitokoto.json
image:
  jsonPath: data/image.json
  imagePath: image

spring:
  ai:
    deepseek:
      base-url: https://api.deepseek.com/v1
      api-key: ***********************************
      chat:
        options:
          model: deepseek-chat
          temperature: 0.8
    # ChatMemory JDBC 配置
    chat:
      memory:
        repository:
          jdbc:
            initialize-schema: never  # 禁用自动创建表，使用我们自己的schema
  # H2 数据库配置
  datasource:
    url: jdbc:h2:mem:chatmemory  # 内存模式，用于测试连接
    driver-class-name: org.h2.Driver
    username: sa
    password:
  sql:
    init:
      schema-locations: classpath:schema-h2.sql  # 指定schema文件位置
      mode: always  # 总是执行schema初始化
  h2:
    console:
      enabled: true  # 启用H2控制台，可以通过 http://localhost:8088/h2-console 访问
server:
  port: 8088