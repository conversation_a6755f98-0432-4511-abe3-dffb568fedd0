hitokoto:
  jsonPath: data/hitokoto.json
image:
  jsonPath: data/image.json
  imagePath: image

spring:
  ai:
    deepseek:
      base-url: https://api.deepseek.com/v1
      api-key: ***********************************
      chat:
        options:
          model: deepseek-chat
          temperature: 0.8
    # ChatMemory JDBC 配置
    chat:
      memory:
        repository:
          jdbc:
            initialize-schema: always  # 让Spring AI自动创建表
  # HSQLDB 数据库配置
  datasource:
    url: **********************************  # 文件模式，保存到 ./data/ 目录
    driver-class-name: org.hsqldb.jdbc.JDBCDriver
    username: sa
    password:
  # 启用H2控制台查看HSQLDB数据
  h2:
    console:
      enabled: true
server:
  port: 8088