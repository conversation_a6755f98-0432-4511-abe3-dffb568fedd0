hitokoto:
  jsonPath: data/hitokoto.json
image:
  jsonPath: data/image.json
  imagePath: image

spring:
  ai:
    deepseek:
      base-url: https://api.deepseek.com/v1
      api-key: ***********************************
      chat:
        options:
          model: deepseek-chat
          temperature: 0.8
    # ChatMemory JDBC 配置
    chat:
      memory:
        repository:
          jdbc:
            initialize-schema: always  # 让Spring AI自动创建表
  # MySQL 数据库配置
  datasource:
    url: ***********************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: 123456  # Docker MySQL密码
  jpa:
    hibernate:
      ddl-auto: update  # 自动创建/更新表结构
    show-sql: true  # 显示SQL语句（可选）
server:
  port: 8088